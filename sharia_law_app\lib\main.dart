import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';

void main() {
  runApp(const ShariaLawApp());
}

class ShariaLawApp extends StatelessWidget {
  const ShariaLawApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'تطبيق الشريعة والقانون',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF6366F1),
          brightness: Brightness.light,
          dynamicSchemeVariant: DynamicSchemeVariant.tonalSpot,
        ),
        fontFamily: GoogleFonts.cairo().fontFamily,
        textTheme: const TextTheme(
          displayLarge: TextStyle(
            fontSize: 57,
            fontWeight: FontWeight.w400,
            letterSpacing: -0.25,
          ),
          displayMedium: TextStyle(fontSize: 45, fontWeight: FontWeight.w400),
          displaySmall: TextStyle(fontSize: 36, fontWeight: FontWeight.w400),
          headlineLarge: TextStyle(fontSize: 32, fontWeight: FontWeight.w600),
          headlineMedium: TextStyle(fontSize: 28, fontWeight: FontWeight.w600),
          headlineSmall: TextStyle(fontSize: 24, fontWeight: FontWeight.w600),
          titleLarge: TextStyle(fontSize: 22, fontWeight: FontWeight.w500),
          titleMedium: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            letterSpacing: 0.15,
          ),
          titleSmall: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            letterSpacing: 0.1,
          ),
          bodyLarge: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w400,
            letterSpacing: 0.5,
          ),
          bodyMedium: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w400,
            letterSpacing: 0.25,
          ),
          bodySmall: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w400,
            letterSpacing: 0.4,
          ),
          labelLarge: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            letterSpacing: 0.1,
          ),
          labelMedium: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            letterSpacing: 0.5,
          ),
          labelSmall: TextStyle(
            fontSize: 11,
            fontWeight: FontWeight.w500,
            letterSpacing: 0.5,
          ),
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            elevation: 1,
            shadowColor: Colors.transparent,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
        ),
        filledButtonTheme: FilledButtonThemeData(
          style: FilledButton.styleFrom(
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
        ),
        cardTheme: CardTheme(
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(
              color: const Color(0xFF6366F1).withValues(alpha: 0.12),
              width: 1,
            ),
          ),
          margin: const EdgeInsets.all(8),
        ),
        appBarTheme: const AppBarTheme(
          elevation: 0,
          scrolledUnderElevation: 1,
          backgroundColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
        ),
      ),
      darkTheme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF6366F1),
          brightness: Brightness.dark,
          dynamicSchemeVariant: DynamicSchemeVariant.tonalSpot,
        ),
        fontFamily: GoogleFonts.cairo().fontFamily,
      ),

      locale: const Locale('ar', 'SA'),
      home: const MainScreen(),

      builder: (context, child) {
        SystemChrome.setSystemUIOverlayStyle(
          const SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: Brightness.dark,
            systemNavigationBarColor: Colors.white,
            systemNavigationBarIconBrightness: Brightness.dark,
          ),
        );

        return Directionality(textDirection: TextDirection.rtl, child: child!);
      },
    );
  }
}

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const HomeScreen(),
    const CommunityScreen(),
    const ProfileScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_currentIndex],
      bottomNavigationBar: _buildGlassBottomNavBar(),
    );
  }

  Widget _buildGlassBottomNavBar() {
    return Container(
      margin: const EdgeInsets.fromLTRB(20, 0, 20, 20),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(30),
        child: Container(
          height: 65,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white.withValues(alpha: 0.9),
                Colors.white.withValues(alpha: 0.8),
              ],
            ),
            borderRadius: BorderRadius.circular(30),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.5),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.15),
                blurRadius: 25,
                offset: const Offset(0, 8),
              ),
              BoxShadow(
                color: Colors.white.withValues(alpha: 0.7),
                blurRadius: 15,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildGlassNavItem(
                label: 'الرئيسية',
                index: 0,
                icon: Icons.home_rounded,
              ),
              _buildGlassNavItem(
                label: 'المجتمع',
                index: 1,
                icon: Icons.people_rounded,
              ),
              _buildGlassNavItem(
                label: 'الملف الشخصي',
                index: 2,
                icon: Icons.person_rounded,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGlassNavItem({
    required String label,
    required int index,
    required IconData icon,
  }) {
    final isSelected = _currentIndex == index;
    final colorScheme = Theme.of(context).colorScheme;

    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _currentIndex = index;
          });
          HapticFeedback.lightImpact();
        },
        child: Container(
          height: 45,
          decoration: BoxDecoration(
            color:
                isSelected
                    ? colorScheme.primary.withValues(alpha: 0.15)
                    : Colors.transparent,
            borderRadius: BorderRadius.circular(25),
          ),
          child: Center(
            child: Icon(
              icon,
              size: isSelected ? 28 : 24,
              color:
                  isSelected
                      ? colorScheme.primary
                      : colorScheme.onSurfaceVariant.withValues(alpha: 0.8),
            ),
          ),
        ),
      ),
    );
  }
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final List<AcademicYear> academicYears = [
    AcademicYear(
      id: 'year1',
      name: 'الفرقة الأولى',
      subjects: 12,
      color: const Color(0xFF6366F1),
      semesters: [
        Semester(
          id: 'year1_sem1',
          name: 'الترم الأول',
          subjects: [
            Subject(name: 'قضايا فقهية معاصرة', lectures: 2, tests: 3),
            Subject(name: 'تاريخ التشريع', lectures: 2, tests: 4),
            Subject(name: 'مصطلح الحديث ورجاله', lectures: 1, tests: 3),
            Subject(name: 'علم الإجرام والعقاب', lectures: 2, tests: 2),
            Subject(name: 'تاريخ القانون', lectures: 1, tests: 3),
          ],
        ),
        Semester(
          id: 'year1_sem2',
          name: 'الترم الثاني',
          subjects: [
            Subject(name: 'القرآن الكريم', lectures: 2, tests: 3),
            Subject(name: 'القانون الدولي العام', lectures: 2, tests: 4),
            Subject(
              name: 'أصول الفقه (حنفي / غير حنفي)',
              lectures: 1,
              tests: 3,
            ),
            Subject(name: 'اللغة الأجنبية', lectures: 2, tests: 2),
            Subject(name: 'النظم السياسية والدستورية', lectures: 1, tests: 3),
            Subject(name: 'المدخل للعلوم القانونية', lectures: 2, tests: 4),
            Subject(
              name: 'فقه مذهبي (حنفي - مالكي - شافعي)',
              lectures: 1,
              tests: 3,
            ),
          ],
        ),
      ],
    ),
    AcademicYear(
      id: 'year2',
      name: 'الفرقة الثانية',
      subjects: 11,
      color: const Color(0xFFEC4899),
      semesters: [
        Semester(
          id: 'year2_sem1',
          name: 'الترم الأول',
          subjects: [
            Subject(name: 'المنظمات الدولية', lectures: 2, tests: 3),
            Subject(name: 'فقه مقارن', lectures: 2, tests: 4),
            Subject(name: 'الاقتصاد', lectures: 1, tests: 3),
            Subject(name: 'قضايا فقهية معاصرة', lectures: 2, tests: 2),
          ],
        ),
        Semester(
          id: 'year2_sem2',
          name: 'الترم الثاني',
          subjects: [
            Subject(name: 'القرآن الكريم', lectures: 2, tests: 3),
            Subject(name: 'القانون المدني', lectures: 2, tests: 4),
            Subject(name: 'اللغة الأجنبية', lectures: 1, tests: 3),
            Subject(name: 'القانون الإداري', lectures: 2, tests: 2),
            Subject(name: 'القانون الجنائي', lectures: 1, tests: 3),
            Subject(name: 'الفقه المقارن', lectures: 2, tests: 4),
            Subject(
              name: 'أصول الفقه (حنفي / غير حنفي)',
              lectures: 1,
              tests: 3,
            ),
          ],
        ),
      ],
    ),
    AcademicYear(
      id: 'year3',
      name: 'الفرقة الثالثة',
      subjects: 13,
      color: const Color(0xFF10B981),
      semesters: [
        Semester(
          id: 'year3_sem1',
          name: 'الترم الأول',
          subjects: [
            Subject(name: 'القانون المدني (عقود)', lectures: 2, tests: 3),
            Subject(name: 'النقود والبنوك', lectures: 2, tests: 4),
            Subject(name: 'الفقه المقارن', lectures: 1, tests: 3),
            Subject(name: 'المصطلحات القانونية', lectures: 2, tests: 2),
          ],
        ),
        Semester(
          id: 'year3_sem2',
          name: 'الترم الثاني',
          subjects: [
            Subject(name: 'القرآن الكريم', lectures: 2, tests: 3),
            Subject(
              name: 'الفقه المذهبي (حنفي - مالكي - شافعي)',
              lectures: 2,
              tests: 4,
            ),
            Subject(name: 'الفقه المقارن', lectures: 1, tests: 3),
            Subject(name: 'القانون الجنائي', lectures: 2, tests: 2),
            Subject(name: 'قانون المرافعات', lectures: 1, tests: 3),
            Subject(name: 'قضاء إداري', lectures: 2, tests: 4),
            Subject(name: 'قانون تجاري', lectures: 1, tests: 3),
            Subject(
              name: 'أصول الفقه (حنفي / غير حنفي)',
              lectures: 2,
              tests: 2,
            ),
            Subject(name: 'أحوال شخصية للمسلمين', lectures: 1, tests: 3),
          ],
        ),
      ],
    ),
    AcademicYear(
      id: 'year4',
      name: 'الفرقة الرابعة',
      subjects: 14,
      color: const Color(0xFFF59E0B),
      semesters: [
        Semester(
          id: 'year4_sem1',
          name: 'الترم الأول',
          subjects: [
            Subject(name: 'التنفيذ الجبري', lectures: 2, tests: 3),
            Subject(name: 'القانون التجاري', lectures: 2, tests: 4),
            Subject(name: 'العقود الإدارية', lectures: 1, tests: 3),
            Subject(name: 'الفقه المقارن', lectures: 2, tests: 2),
            Subject(name: 'الميراث', lectures: 1, tests: 3),
          ],
        ),
        Semester(
          id: 'year4_sem2',
          name: 'الترم الثاني',
          subjects: [
            Subject(name: 'القرآن الكريم', lectures: 2, tests: 3),
            Subject(name: 'الملكية والتأمينات', lectures: 2, tests: 4),
            Subject(name: 'الإجراءات الجنائية', lectures: 1, tests: 3),
            Subject(name: 'قانون العمل', lectures: 2, tests: 2),
            Subject(
              name: 'المالية العامة والتشريع الضريبي',
              lectures: 1,
              tests: 3,
            ),
            Subject(name: 'القانون الدولي الخاص', lectures: 2, tests: 4),
            Subject(
              name: 'أصول الفقه (حنفي / غير حنفي)',
              lectures: 1,
              tests: 3,
            ),
            Subject(
              name: 'الفقه (مالكي - شافعي - حنبلي)',
              lectures: 2,
              tests: 2,
            ),
            Subject(name: 'أحوال شخصية لغير المسلمين', lectures: 1, tests: 3),
          ],
        ),
      ],
    ),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: CustomScrollView(
            physics: const BouncingScrollPhysics(),
            slivers: [
              // Modern Hero Header
              SliverToBoxAdapter(
                child: Container(
                  height: 280,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        const Color(0xFF667EEA),
                        const Color(0xFF764BA2),
                        const Color(0xFF6366F1),
                      ],
                    ),
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(32),
                      bottomRight: Radius.circular(32),
                    ),
                  ),
                  child: Stack(
                    children: [
                      // Header Content
                      Positioned(
                        top: 20,
                        left: 20,
                        right: 20,
                        child: Container(
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.15),
                            borderRadius: BorderRadius.circular(24),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.2),
                              width: 1,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: 20,
                                offset: const Offset(0, 8),
                              ),
                            ],
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'أهلاً وسهلاً! 🌟',
                                      style: GoogleFonts.cairo(
                                        fontSize: 28,
                                        fontWeight: FontWeight.w800,
                                        color: Colors.white,
                                        height: 1.2,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      'مرحباً بك في تطبيق كلية الشريعة والقانون',
                                      style: GoogleFonts.cairo(
                                        fontSize: 14,
                                        color: Colors.white.withValues(
                                          alpha: 0.9,
                                        ),
                                        height: 1.4,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              // Notification Button
                              Container(
                                width: 50,
                                height: 50,
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(16),
                                  border: Border.all(
                                    color: Colors.white.withValues(alpha: 0.3),
                                    width: 1,
                                  ),
                                ),
                                child: IconButton(
                                  onPressed: () => _showNotifications(),
                                  icon: Stack(
                                    children: [
                                      const Icon(
                                        Icons.notifications_rounded,
                                        color: Colors.white,
                                        size: 24,
                                      ),
                                      Positioned(
                                        right: 2,
                                        top: 2,
                                        child: Container(
                                          width: 10,
                                          height: 10,
                                          decoration: BoxDecoration(
                                            color: const Color(0xFFFF4757),
                                            borderRadius: BorderRadius.circular(
                                              5,
                                            ),
                                            border: Border.all(
                                              color: Colors.white,
                                              width: 1,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      // Stats Cards
                      Positioned(
                        bottom: -30,
                        left: 20,
                        right: 20,
                        child: Row(
                          children: [
                            Expanded(
                              child: _buildSimpleStatCard(
                                icon: Icons.school_rounded,
                                title: '4',
                                subtitle: 'فرق دراسية',
                                color: const Color(0xFF6366F1),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: _buildSimpleStatCard(
                                icon: Icons.auto_stories_rounded,
                                title: '50+',
                                subtitle: 'مادة دراسية',
                                color: const Color(0xFFEC4899),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: _buildSimpleStatCard(
                                icon: Icons.picture_as_pdf_rounded,
                                title: '200+',
                                subtitle: 'ملف PDF',
                                color: const Color(0xFF10B981),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Spacing
              const SliverToBoxAdapter(child: SizedBox(height: 50)),

              // Section Title
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الفرق الدراسية',
                        style: GoogleFonts.cairo(
                          fontSize: 24,
                          fontWeight: FontWeight.w700,
                          color: const Color(0xFF1F2937),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'اختر الفرقة الدراسية للوصول إلى المواد والمحاضرات',
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          color: const Color(0xFF6B7280),
                        ),
                      ),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),

              // Academic Years List
              SliverPadding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                sliver: SliverList(
                  delegate: SliverChildBuilderDelegate((context, index) {
                    final year = academicYears[index];
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: _buildSimpleYearCard(year, index),
                    );
                  }, childCount: academicYears.length),
                ),
              ),

              // Bottom spacing
              const SliverToBoxAdapter(child: SizedBox(height: 100)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSimpleStatCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(18),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.15),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: color.withValues(alpha: 0.1), width: 1),
      ),
      child: Column(
        children: [
          Container(
            width: 44,
            height: 44,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [color, color.withValues(alpha: 0.8)],
              ),
              borderRadius: BorderRadius.circular(14),
              boxShadow: [
                BoxShadow(
                  color: color.withValues(alpha: 0.25),
                  blurRadius: 6,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: Icon(icon, color: Colors.white, size: 22),
          ),
          const SizedBox(height: 10),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.w800,
              color: const Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 2),
          Text(
            subtitle,
            style: GoogleFonts.cairo(
              fontSize: 11,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF6B7280),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSimpleYearCard(AcademicYear year, int index) {
    final colors = [
      [const Color(0xFF667EEA), const Color(0xFF764BA2)],
      [const Color(0xFFFF6B6B), const Color(0xFFFFE66D)],
      [const Color(0xFF4ECDC4), const Color(0xFF44A08D)],
      [const Color(0xFFFFB347), const Color(0xFFFFCC02)],
    ];

    final cardColors = colors[index % colors.length];

    return GestureDetector(
      onTap: () => _navigateToSubjects(year),
      child: Container(
        height: 120,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: cardColors[0].withValues(alpha: 0.25),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Stack(
          children: [
            // Background Gradient
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: cardColors,
                ),
                borderRadius: BorderRadius.circular(20),
              ),
            ),

            // Content
            Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  // Year Number
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.3),
                        width: 2,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        '${index + 1}',
                        style: GoogleFonts.cairo(
                          fontSize: 24,
                          fontWeight: FontWeight.w900,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // Year Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          year.name,
                          style: GoogleFonts.cairo(
                            fontSize: 20,
                            fontWeight: FontWeight.w800,
                            color: Colors.white,
                            height: 1.2,
                          ),
                        ),
                        const SizedBox(height: 6),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 10,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.auto_stories_rounded,
                                color: Colors.white,
                                size: 14,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '${year.subjects} مادة',
                                style: GoogleFonts.cairo(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Arrow Icon
                  Container(
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.arrow_forward_ios_rounded,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToSubjects(AcademicYear year) {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => SubjectsScreen(year: year)));
  }

  void _showNotifications() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(Icons.notifications, color: Color(0xFF6366F1)),
                SizedBox(width: 8),
                Text('الإشعارات'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildNotificationItem(
                  'إعلان جديد',
                  'تم إضافة محاضرة جديدة في مادة القانون المدني',
                  '5 دقائق',
                  Icons.school,
                ),
                _buildNotificationItem(
                  'تذكير',
                  'موعد الامتحان غداً الساعة 9 صباحاً',
                  '1 ساعة',
                  Icons.alarm,
                ),
                _buildNotificationItem(
                  'رسالة جديدة',
                  'رد جديد على منشورك في المجتمع',
                  '3 ساعات',
                  Icons.message,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  Widget _buildNotificationItem(
    String title,
    String content,
    String time,
    IconData icon,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Color(0xFFF8FAFC),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Color(0xFF6366F1).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(icon, color: Color(0xFF6366F1), size: 20),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                ),
                Text(
                  content,
                  style: TextStyle(color: Color(0xFF64748B), fontSize: 12),
                ),
                Text(
                  time,
                  style: TextStyle(color: Color(0xFF9CA3AF), fontSize: 11),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// Modern Pattern Painter for Header Background
class ModernPatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = Colors.white.withValues(alpha: 0.1)
          ..style = PaintingStyle.fill;

    // Create modern geometric patterns
    for (int i = 0; i < 20; i++) {
      final x = (i * 60.0) % size.width;
      final y = (i * 40.0) % size.height;

      // Draw circles
      canvas.drawCircle(Offset(x, y), 20 + (i % 3) * 10, paint);

      // Draw rounded rectangles
      final rect = RRect.fromRectAndRadius(
        Rect.fromLTWH(x + 100, y + 50, 40, 20),
        const Radius.circular(10),
      );
      canvas.drawRRect(rect, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Card Pattern Painter for Year Cards
class CardPatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = Colors.white.withValues(alpha: 0.1)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1;

    // Create subtle geometric patterns
    for (int i = 0; i < 5; i++) {
      final x = size.width - (i * 30.0);
      final y = i * 25.0;

      // Draw diagonal lines
      canvas.drawLine(Offset(x, 0), Offset(x - 50, y + 50), paint);

      // Draw small circles
      canvas.drawCircle(Offset(x - 20, y + 20), 5, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class YearCard extends StatelessWidget {
  final AcademicYear year;
  final int index;
  final VoidCallback onTap;

  const YearCard({
    super.key,
    required this.year,
    required this.index,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 100,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [year.color, year.color.withValues(alpha: 0.8)],
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: year.color.withValues(alpha: 0.3),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Center(
                  child: Text(
                    '${index + 1}',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 16),

              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,

                  children: [
                    Text(
                      year.name,
                      style: Theme.of(
                        context,
                      ).textTheme.headlineMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.book_outlined,
                          color: Colors.white.withValues(alpha: 0.8),
                          size: 14,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${year.subjects} مادة دراسية',
                          style: Theme.of(
                            context,
                          ).textTheme.bodySmall?.copyWith(
                            color: Colors.white.withValues(alpha: 0.8),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              Icon(Icons.arrow_forward_ios, color: Colors.white, size: 16),
            ],
          ),
        ),
      ),
    );
  }
}

class ModernYearCard extends StatelessWidget {
  final AcademicYear year;
  final int index;
  final VoidCallback onTap;

  const ModernYearCard({
    super.key,
    required this.year,
    required this.index,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Card(
      elevation: 0,
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                year.color.withValues(alpha: 0.05),
                year.color.withValues(alpha: 0.02),
              ],
            ),
          ),
          child: Row(
            children: [
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  color: year.color.withValues(alpha: 0.12),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Center(
                  child: Text(
                    '${index + 1}',
                    style: GoogleFonts.cairo(
                      fontSize: 24,
                      color: year.color,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      year.name,
                      style: GoogleFonts.cairo(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.book_rounded,
                          size: 16,
                          color: colorScheme.onSurfaceVariant,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${year.subjects} مادة دراسية',
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            color: colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: year.color.withValues(alpha: 0.12),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.arrow_forward_ios_rounded,
                  color: year.color,
                  size: 16,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class SubjectsScreen extends StatefulWidget {
  final AcademicYear year;

  const SubjectsScreen({super.key, required this.year});

  @override
  State<SubjectsScreen> createState() => _SubjectsScreenState();
}

class _SubjectsScreenState extends State<SubjectsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: widget.year.semesters.length,
      vsync: this,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: SafeArea(
        child: Column(
          children: [
            // Header with gradient
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    widget.year.color,
                    widget.year.color.withValues(alpha: 0.8),
                  ],
                ),
              ),
              child: Column(
                children: [
                  // App Bar
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        IconButton(
                          onPressed: () => Navigator.of(context).pop(),
                          icon: const Icon(
                            Icons.arrow_back_ios,
                            color: Colors.white,
                          ),
                        ),
                        Expanded(
                          child: Text(
                            widget.year.name,
                            style: Theme.of(
                              context,
                            ).textTheme.headlineMedium?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        const SizedBox(width: 48), // Balance the back button
                      ],
                    ),
                  ),

                  // Tab Bar
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: TabBar(
                      controller: _tabController,
                      indicator: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(25),
                      ),
                      labelColor: widget.year.color,
                      unselectedLabelColor: Colors.white,
                      labelStyle: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                      unselectedLabelStyle: const TextStyle(
                        fontWeight: FontWeight.normal,
                        fontSize: 14,
                      ),
                      tabs:
                          widget.year.semesters
                              .map((semester) => Tab(text: semester.name))
                              .toList(),
                    ),
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),

            // Tab Bar View
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children:
                    widget.year.semesters
                        .map((semester) => _buildSemesterView(semester))
                        .toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSemesterView(Semester semester) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: semester.subjects.length,
      itemBuilder: (context, index) {
        final subject = semester.subjects[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: _buildSubjectCard(subject),
        );
      },
    );
  }

  Widget _buildSubjectCard(Subject subject) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: widget.year.color.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Subject Icon
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: widget.year.color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.book_outlined,
                color: widget.year.color,
                size: 24,
              ),
            ),

            const SizedBox(width: 16),

            // Subject Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    subject.name,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF1F2937),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      _buildInfoChip(
                        icon: Icons.play_circle_outline,
                        text: '${subject.lectures} محاضرات',
                        color: const Color(0xFF10B981),
                      ),
                      const SizedBox(width: 12),
                      _buildInfoChip(
                        icon: Icons.quiz_outlined,
                        text: '${subject.tests} اختبارات',
                        color: const Color(0xFFEC4899),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Arrow Icon
            Icon(
              Icons.arrow_forward_ios,
              color: widget.year.color.withValues(alpha: 0.6),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String text,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

// Community Screen
class CommunityScreen extends StatefulWidget {
  const CommunityScreen({super.key});

  @override
  State<CommunityScreen> createState() => _CommunityScreenState();
}

class _CommunityScreenState extends State<CommunityScreen> {
  final TextEditingController _postController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  final List<Post> _posts = [
    Post(
      id: '1',
      authorName: 'أحمد محمد السيد',
      authorAvatar:
          'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      content:
          'مرحباً بالجميع في مجتمع كلية الشريعة والقانون! 🎓\n\nهذا المكان مخصص لتبادل المعرفة والخبرات بين الطلاب والأساتذة. نتطلع لمشاركاتكم المفيدة ونقاشاتكم البناءة.\n\n#كلية_الشريعة_والقانون #التعليم_الجامعي',
      timestamp: DateTime.now().subtract(const Duration(hours: 2)),
      likes: 24,
      comments: 8,
      shares: 3,
      isLiked: false,
      imageUrl:
          'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=600&h=400&fit=crop',
      userRole: 'طالب - الفرقة الثالثة',
    ),
    Post(
      id: '2',
      authorName: 'فاطمة أحمد علي',
      authorAvatar:
          'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
      content:
          'السلام عليكم ورحمة الله وبركاته 🌸\n\nهل يمكن لأحد الزملاء مشاركة ملخص محاضرة أصول الفقه الأخيرة؟ لم أتمكن من الحضور بسبب ظروف طارئة.\n\nجزاكم الله خيراً 🤲',
      timestamp: DateTime.now().subtract(const Duration(hours: 5)),
      likes: 12,
      comments: 15,
      shares: 2,
      isLiked: true,
      userRole: 'طالبة - الفرقة الثانية',
    ),
    Post(
      id: '3',
      authorName: 'د. محمد العلي',
      authorAvatar:
          'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      content:
          '📢 إعلان هام للطلاب\n\nتذكير: موعد الامتحان النهائي لمادة القانون المدني:\n📅 يوم الأحد القادم\n🕘 الساعة 9:00 صباحاً\n📍 القاعة الكبرى\n\nأتمنى للجميع التوفيق والنجاح! 🎯\n\n#امتحانات #القانون_المدني',
      timestamp: DateTime.now().subtract(const Duration(days: 1)),
      likes: 67,
      comments: 23,
      shares: 12,
      isLiked: true,
      userRole: 'أستاذ القانون المدني',
      isPinned: true,
    ),
    Post(
      id: '4',
      authorName: 'سارة محمود',
      authorAvatar:
          'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      content:
          'مشاركة مفيدة 📚\n\nقمت بتلخيص أهم النقاط في محاضرة الفقه المقارن اليوم. الملف متاح للجميع في التعليقات.\n\nنسأل الله التوفيق للجميع! 🤲',
      timestamp: DateTime.now().subtract(const Duration(hours: 8)),
      likes: 31,
      comments: 9,
      shares: 7,
      isLiked: false,
      userRole: 'طالبة - الفرقة الرابعة',
      hasAttachment: true,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F2F5),
      body: SafeArea(
        child: Column(
          children: [
            // Facebook-style Header
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Text(
                    'المجتمع',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF1877F2),
                      fontSize: 24,
                    ),
                  ),
                  const Spacer(),
                  // Search Button
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: const Color(0xFFF0F2F5),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: IconButton(
                      onPressed: () {},
                      icon: const Icon(
                        Icons.search,
                        color: Color(0xFF65676B),
                        size: 20,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Messages Button
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: const Color(0xFFF0F2F5),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: IconButton(
                      onPressed: () {},
                      icon: const Icon(
                        Icons.chat_bubble_outline,
                        color: Color(0xFF65676B),
                        size: 20,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Posts List with Create Post
            Expanded(
              child: CustomScrollView(
                controller: _scrollController,
                slivers: [
                  // Create Post Section
                  SliverToBoxAdapter(
                    child: Container(
                      margin: const EdgeInsets.all(8),
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 2,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      child: _buildCreatePostSection(),
                    ),
                  ),

                  // Posts
                  SliverList(
                    delegate: SliverChildBuilderDelegate((context, index) {
                      return Container(
                        margin: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        child: _buildPostCard(_posts[index]),
                      );
                    }, childCount: _posts.length),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCreatePostSection() {
    return Row(
      children: [
        // Profile Picture
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: const Color(0xFF1877F2),
            borderRadius: BorderRadius.circular(20),
          ),
          child: const Center(
            child: Text('👤', style: TextStyle(fontSize: 20)),
          ),
        ),
        const SizedBox(width: 12),

        // Create Post Input
        Expanded(
          child: GestureDetector(
            onTap: () => _showCreatePostDialog(),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: const Color(0xFFF0F2F5),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: const Color(0xFFE4E6EA), width: 1),
              ),
              child: Text(
                'ما الذي تفكر فيه؟',
                style: TextStyle(color: const Color(0xFF65676B), fontSize: 16),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPostCard(Post post) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Post Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Profile Picture
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    color: const Color(0xFF1877F2).withValues(alpha: 0.1),
                  ),
                  child: Center(
                    child: Text('👤', style: const TextStyle(fontSize: 20)),
                  ),
                ),
                const SizedBox(width: 12),

                // Author Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        post.authorName,
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 15,
                          color: Color(0xFF050505),
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        _formatTimestamp(post.timestamp),
                        style: const TextStyle(
                          color: Color(0xFF65676B),
                          fontSize: 13,
                        ),
                      ),
                    ],
                  ),
                ),

                // More Options
                IconButton(
                  onPressed: () {},
                  icon: const Icon(
                    Icons.more_horiz,
                    color: Color(0xFF65676B),
                    size: 20,
                  ),
                ),
              ],
            ),
          ),

          // Post Content
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              post.content,
              style: const TextStyle(
                color: Color(0xFF050505),
                fontSize: 15,
                height: 1.33,
              ),
            ),
          ),

          const SizedBox(height: 12),

          // Reactions Summary
          if (post.likes > 0 || post.comments > 0)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  if (post.likes > 0) ...[
                    Container(
                      width: 18,
                      height: 18,
                      decoration: BoxDecoration(
                        color: const Color(0xFF1877F2),
                        borderRadius: BorderRadius.circular(9),
                      ),
                      child: const Icon(
                        Icons.thumb_up,
                        color: Colors.white,
                        size: 10,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${post.likes}',
                      style: const TextStyle(
                        color: Color(0xFF65676B),
                        fontSize: 13,
                      ),
                    ),
                  ],
                  const Spacer(),
                  if (post.comments > 0)
                    Text(
                      '${post.comments} تعليق',
                      style: const TextStyle(
                        color: Color(0xFF65676B),
                        fontSize: 13,
                      ),
                    ),
                ],
              ),
            ),

          // Divider
          if (post.likes > 0 || post.comments > 0)
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              height: 1,
              color: const Color(0xFFE4E6EA),
            ),

          // Action Buttons
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
            child: Row(
              children: [
                Expanded(
                  child: _buildFacebookActionButton(
                    icon:
                        post.isLiked ? Icons.thumb_up : Icons.thumb_up_outlined,
                    label: 'إعجاب',
                    color:
                        post.isLiked
                            ? const Color(0xFF1877F2)
                            : const Color(0xFF65676B),
                    onTap: () => _toggleLike(post),
                  ),
                ),
                Expanded(
                  child: _buildFacebookActionButton(
                    icon: Icons.chat_bubble_outline,
                    label: 'تعليق',
                    color: const Color(0xFF65676B),
                    onTap: () => _showCommentsDialog(post),
                  ),
                ),
                Expanded(
                  child: _buildFacebookActionButton(
                    icon: Icons.share_outlined,
                    label: 'مشاركة',
                    color: const Color(0xFF65676B),
                    onTap: () => _sharePost(post),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFacebookActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w600,
                fontSize: 15,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }

  void _showCreatePostDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إنشاء منشور جديد'),
            content: TextField(
              controller: _postController,
              maxLines: 4,
              decoration: const InputDecoration(
                hintText: 'ما الذي تريد مشاركته؟',
                border: OutlineInputBorder(),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  if (_postController.text.isNotEmpty) {
                    _createPost(_postController.text);
                    _postController.clear();
                    Navigator.pop(context);
                  }
                },
                child: const Text('نشر'),
              ),
            ],
          ),
    );
  }

  void _createPost(String content) {
    setState(() {
      _posts.insert(
        0,
        Post(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          authorName: 'أنت',
          authorAvatar: '👤',
          content: content,
          timestamp: DateTime.now(),
          likes: 0,
          comments: 0,
          shares: 0,
          isLiked: false,
        ),
      );
    });
  }

  void _toggleLike(Post post) {
    setState(() {
      post.isLiked = !post.isLiked;
      post.likes += post.isLiked ? 1 : -1;
    });
  }

  void _showCommentsDialog(Post post) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('التعليقات'),
            content: const Text('سيتم إضافة نظام التعليقات قريباً'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  void _sharePost(Post post) {
    setState(() {
      post.shares += 1;
    });
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('تم مشاركة المنشور')));
  }
}

// Profile Screen
class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  bool _isDarkMode = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          _isDarkMode ? const Color(0xFF121212) : const Color(0xFFF8FAFC),
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Animated Header with Glassmorphism Effect
                  Container(
                    height: 300,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors:
                            _isDarkMode
                                ? [
                                  const Color(0xFF1E1E2E),
                                  const Color(0xFF2D2D44),
                                  const Color(0xFF3E3E5E),
                                ]
                                : [
                                  const Color(0xFF667EEA),
                                  const Color(0xFF764BA2),
                                  const Color(0xFF6366F1),
                                ],
                      ),
                    ),
                    child: Stack(
                      children: [
                        // Animated Background Circles
                        Positioned(
                          top: -50,
                          right: -50,
                          child: Container(
                            width: 150,
                            height: 150,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.white.withValues(alpha: 0.1),
                            ),
                          ),
                        ),
                        Positioned(
                          bottom: -30,
                          left: -30,
                          child: Container(
                            width: 100,
                            height: 100,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.white.withValues(alpha: 0.05),
                            ),
                          ),
                        ),

                        // Profile Content
                        Padding(
                          padding: const EdgeInsets.all(24),
                          child: Column(
                            children: [
                              const SizedBox(height: 20),

                              // Profile Picture with Glow Effect
                              Container(
                                width: 120,
                                height: 120,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  gradient: LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      Colors.white.withValues(alpha: 0.3),
                                      Colors.white.withValues(alpha: 0.1),
                                    ],
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.white.withValues(
                                        alpha: 0.3,
                                      ),
                                      blurRadius: 20,
                                      spreadRadius: 5,
                                    ),
                                  ],
                                ),
                                child: Container(
                                  margin: const EdgeInsets.all(4),
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    gradient: LinearGradient(
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                      colors: [
                                        const Color(0xFF667EEA),
                                        const Color(0xFF764BA2),
                                      ],
                                    ),
                                  ),
                                  child: const Center(
                                    child: Text(
                                      '👨‍🎓',
                                      style: TextStyle(fontSize: 50),
                                    ),
                                  ),
                                ),
                              ),

                              const SizedBox(height: 20),

                              // Name with Glow Effect
                              Text(
                                'أحمد محمد علي السيد',
                                style: TextStyle(
                                  fontSize: 28,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                  shadows: [
                                    Shadow(
                                      color: Colors.white.withValues(
                                        alpha: 0.5,
                                      ),
                                      blurRadius: 10,
                                    ),
                                  ],
                                ),
                                textAlign: TextAlign.center,
                              ),

                              const SizedBox(height: 8),

                              // Student Info
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                    color: Colors.white.withValues(alpha: 0.3),
                                    width: 1,
                                  ),
                                ),
                                child: Text(
                                  'طالب - الفرقة الثالثة • كلية الشريعة والقانون',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.white.withValues(alpha: 0.9),
                                    fontWeight: FontWeight.w500,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Modern Action Cards
                  Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      children: [
                        // Primary Actions
                        Row(
                          children: [
                            Expanded(
                              child: _buildActionCard(
                                icon: Icons.edit_outlined,
                                title: 'تعديل الملف الشخصي',
                                subtitle: 'تحديث معلوماتك الشخصية',
                                color: const Color(0xFF6366F1),
                                onTap: () => _editProfile(),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: _buildActionCard(
                                icon: Icons.download_outlined,
                                title: 'التحميلات',
                                subtitle: 'ملفاتك المحملة',
                                color: const Color(0xFF10B981),
                                onTap: () => _showDownloads(),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 16),

                        // Settings Card
                        _buildSettingsCard(),

                        const SizedBox(height: 16),

                        // Dark Mode Toggle
                        _buildDarkModeCard(),

                        const SizedBox(height: 24),

                        // Logout Button
                        _buildLogoutButton(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: _isDarkMode ? const Color(0xFF1E1E2E) : Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.2),
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
          border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
        ),
        child: Column(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: _isDarkMode ? Colors.white : const Color(0xFF1F2937),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: _isDarkMode ? Colors.white70 : const Color(0xFF6B7280),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: _isDarkMode ? const Color(0xFF1E1E2E) : Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: const Color(0xFFEC4899).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(15),
            ),
            child: const Icon(
              Icons.settings_outlined,
              color: Color(0xFFEC4899),
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الإعدادات',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _isDarkMode ? Colors.white : const Color(0xFF1F2937),
                  ),
                ),
                Text(
                  'إدارة إعدادات التطبيق',
                  style: TextStyle(
                    fontSize: 12,
                    color:
                        _isDarkMode ? Colors.white70 : const Color(0xFF6B7280),
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.arrow_forward_ios,
            size: 16,
            color: _isDarkMode ? Colors.white70 : const Color(0xFF9CA3AF),
          ),
        ],
      ),
    );
  }

  Widget _buildDarkModeCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: _isDarkMode ? const Color(0xFF1E1E2E) : Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: const Color(0xFFF59E0B).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(15),
            ),
            child: Icon(
              _isDarkMode ? Icons.dark_mode : Icons.light_mode,
              color: const Color(0xFFF59E0B),
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _isDarkMode ? 'الوضع المظلم' : 'الوضع الفاتح',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _isDarkMode ? Colors.white : const Color(0xFF1F2937),
                  ),
                ),
                Text(
                  'تغيير مظهر التطبيق',
                  style: TextStyle(
                    fontSize: 12,
                    color:
                        _isDarkMode ? Colors.white70 : const Color(0xFF6B7280),
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: _isDarkMode,
            onChanged: (value) {
              setState(() {
                _isDarkMode = value;
              });
            },
            activeColor: const Color(0xFFF59E0B),
          ),
        ],
      ),
    );
  }

  Widget _buildLogoutButton() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.red.withValues(alpha: 0.8), Colors.red],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.red.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.logout_outlined, color: Colors.white, size: 24),
          const SizedBox(width: 12),
          const Text(
            'تسجيل الخروج',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _editProfile() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تعديل الملف الشخصي'),
            content: const Text('سيتم إضافة هذه الميزة قريباً'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  void _showDownloads() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(Icons.download, color: Color(0xFF10B981)),
                SizedBox(width: 8),
                Text('التحميلات'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildDownloadItem(
                  'ملخص القانون المدني.pdf',
                  '2.5 MB',
                  '15 نوفمبر',
                ),
                _buildDownloadItem(
                  'محاضرة أصول الفقه.pdf',
                  '1.8 MB',
                  '12 نوفمبر',
                ),
                _buildDownloadItem('نماذج امتحانات.pdf', '3.2 MB', '10 نوفمبر'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  Widget _buildDownloadItem(String name, String size, String date) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Color(0xFFF8FAFC),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Color(0xFF10B981).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.picture_as_pdf,
              color: Color(0xFF10B981),
              size: 20,
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                ),
                Text(
                  '$size • $date',
                  style: TextStyle(color: Color(0xFF64748B), fontSize: 12),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {},
            icon: Icon(Icons.download, color: Color(0xFF10B981), size: 20),
          ),
        ],
      ),
    );
  }
}

// Post Model
class Post {
  final String id;
  final String authorName;
  final String authorAvatar;
  final String content;
  final DateTime timestamp;
  int likes;
  int comments;
  int shares;
  bool isLiked;
  final String? imageUrl;
  final String? userRole;
  final bool isPinned;
  final bool hasAttachment;

  Post({
    required this.id,
    required this.authorName,
    required this.authorAvatar,
    required this.content,
    required this.timestamp,
    required this.likes,
    required this.comments,
    required this.shares,
    required this.isLiked,
    this.imageUrl,
    this.userRole,
    this.isPinned = false,
    this.hasAttachment = false,
  });
}

class Subject {
  final String name;
  final int lectures;
  final int tests;

  Subject({required this.name, required this.lectures, required this.tests});
}

class Semester {
  final String id;
  final String name;
  final List<Subject> subjects;

  Semester({required this.id, required this.name, required this.subjects});
}

class AcademicYear {
  final String id;
  final String name;
  final int subjects;
  final Color color;
  final List<Semester> semesters;

  AcademicYear({
    required this.id,
    required this.name,
    required this.subjects,
    required this.color,
    required this.semesters,
  });
}
