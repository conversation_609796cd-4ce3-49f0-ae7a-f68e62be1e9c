import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

void main() {
  runApp(const ShariaLawApp());
}

class ShariaLawApp extends StatelessWidget {
  const ShariaLawApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'كلية الشريعة والقانون',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.blue,
        fontFamily: GoogleFonts.cairo().fontFamily,
      ),
      home: const MainScreen(),
    );
  }
}

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const HomeScreen(),
    const CommunityScreen(),
    const ProfileScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_currentIndex],
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.95),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
          child: BottomNavigationBar(
            currentIndex: _currentIndex,
            onTap: (index) => setState(() => _currentIndex = index),
            backgroundColor: Colors.transparent,
            elevation: 0,
            selectedItemColor: const Color(0xFF6366F1),
            unselectedItemColor: const Color(0xFF9CA3AF),
            selectedLabelStyle: GoogleFonts.cairo(
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
            unselectedLabelStyle: GoogleFonts.cairo(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
            items: const [
              BottomNavigationBarItem(
                icon: Icon(Icons.home_rounded),
                label: 'الرئيسية',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.people_rounded),
                label: 'المجتمع',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.person_rounded),
                label: 'الملف الشخصي',
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Academic Year Model
class AcademicYear {
  final String title;
  final String description;
  final List<Semester> semesters;
  final Color color;

  AcademicYear({
    required this.title,
    required this.description,
    required this.semesters,
    required this.color,
  });
}

// Semester Model
class Semester {
  final String name;
  final List<Subject> subjects;

  Semester({required this.name, required this.subjects});
}

// Subject Model
class Subject {
  final String name;
  final String code;
  final int creditHours;

  Subject({required this.name, required this.code, required this.creditHours});
}

// Academic Years Data
final List<AcademicYear> academicYears = [
  AcademicYear(
    title: 'الفرقة الأولى',
    description: 'السنة الأولى - المواد الأساسية',
    color: const Color(0xFF6366F1),
    semesters: [
      Semester(
        name: 'الفصل الدراسي الأول',
        subjects: [
          Subject(name: 'مدخل إلى علوم الشريعة', code: 'SH101', creditHours: 3),
          Subject(
            name: 'القرآن الكريم وعلومه (1)',
            code: 'QR101',
            creditHours: 3,
          ),
          Subject(
            name: 'الحديث الشريف وعلومه (1)',
            code: 'HD101',
            creditHours: 3,
          ),
          Subject(name: 'اللغة العربية (1)', code: 'AR101', creditHours: 3),
          Subject(name: 'مدخل إلى القانون', code: 'LW101', creditHours: 3),
          Subject(
            name: 'تاريخ التشريع الإسلامي',
            code: 'HI101',
            creditHours: 2,
          ),
        ],
      ),
      Semester(
        name: 'الفصل الدراسي الثاني',
        subjects: [
          Subject(name: 'أصول الفقه (1)', code: 'FQ102', creditHours: 3),
          Subject(
            name: 'القرآن الكريم وعلومه (2)',
            code: 'QR102',
            creditHours: 3,
          ),
          Subject(
            name: 'الحديث الشريف وعلومه (2)',
            code: 'HD102',
            creditHours: 3,
          ),
          Subject(name: 'اللغة العربية (2)', code: 'AR102', creditHours: 3),
          Subject(name: 'القانون الدستوري', code: 'LW102', creditHours: 3),
          Subject(
            name: 'المدخل إلى الفقه الإسلامي',
            code: 'FQ101',
            creditHours: 2,
          ),
        ],
      ),
    ],
  ),
  AcademicYear(
    title: 'الفرقة الثانية',
    description: 'السنة الثانية - التخصص المتوسط',
    color: const Color(0xFFEC4899),
    semesters: [
      Semester(
        name: 'الفصل الدراسي الأول',
        subjects: [
          Subject(name: 'فقه العبادات', code: 'FQ201', creditHours: 4),
          Subject(name: 'أصول الفقه (2)', code: 'FQ202', creditHours: 3),
          Subject(name: 'القانون المدني (1)', code: 'LW201', creditHours: 4),
          Subject(name: 'القانون الإداري (1)', code: 'LW202', creditHours: 3),
          Subject(name: 'التفسير وعلوم القرآن', code: 'QR201', creditHours: 3),
          Subject(name: 'علوم الحديث', code: 'HD201', creditHours: 2),
        ],
      ),
      Semester(
        name: 'الفصل الدراسي الثاني',
        subjects: [
          Subject(name: 'فقه المعاملات (1)', code: 'FQ203', creditHours: 4),
          Subject(name: 'أصول الفقه (3)', code: 'FQ204', creditHours: 3),
          Subject(name: 'القانون المدني (2)', code: 'LW203', creditHours: 4),
          Subject(name: 'القانون الإداري (2)', code: 'LW204', creditHours: 3),
          Subject(name: 'الفقه المقارن', code: 'FQ205', creditHours: 3),
          Subject(name: 'أصول التفسير', code: 'QR202', creditHours: 2),
        ],
      ),
    ],
  ),
  AcademicYear(
    title: 'الفرقة الثالثة',
    description: 'السنة الثالثة - التخصص المتقدم',
    color: const Color(0xFF10B981),
    semesters: [
      Semester(
        name: 'الفصل الدراسي الأول',
        subjects: [
          Subject(name: 'فقه المعاملات (2)', code: 'FQ301', creditHours: 4),
          Subject(name: 'القانون الجنائي (1)', code: 'LW301', creditHours: 4),
          Subject(name: 'قانون الأحوال الشخصية', code: 'LW302', creditHours: 3),
          Subject(name: 'أصول الفقه (4)', code: 'FQ302', creditHours: 3),
          Subject(
            name: 'الفقه الجنائي الإسلامي',
            code: 'FQ303',
            creditHours: 3,
          ),
          Subject(name: 'القضاء في الإسلام', code: 'FQ304', creditHours: 2),
        ],
      ),
      Semester(
        name: 'الفصل الدراسي الثاني',
        subjects: [
          Subject(name: 'فقه الأسرة', code: 'FQ305', creditHours: 4),
          Subject(name: 'القانون الجنائي (2)', code: 'LW303', creditHours: 4),
          Subject(
            name: 'قانون المرافعات المدنية',
            code: 'LW304',
            creditHours: 3,
          ),
          Subject(name: 'الفقه المالي الإسلامي', code: 'FQ306', creditHours: 3),
          Subject(name: 'القانون التجاري', code: 'LW305', creditHours: 3),
          Subject(name: 'الوقف والوصايا', code: 'FQ307', creditHours: 2),
        ],
      ),
    ],
  ),
  AcademicYear(
    title: 'الفرقة الرابعة',
    description: 'السنة الرابعة - التخصص والتطبيق',
    color: const Color(0xFFF59E0B),
    semesters: [
      Semester(
        name: 'الفصل الدراسي الأول',
        subjects: [
          Subject(name: 'فقه النوازل المعاصرة', code: 'FQ401', creditHours: 4),
          Subject(
            name: 'قانون المرافعات الجنائية',
            code: 'LW401',
            creditHours: 4,
          ),
          Subject(name: 'القانون الدولي العام', code: 'LW402', creditHours: 3),
          Subject(name: 'الاقتصاد الإسلامي', code: 'EC401', creditHours: 3),
          Subject(name: 'القانون الدولي الخاص', code: 'LW403', creditHours: 3),
          Subject(name: 'منهج البحث العلمي', code: 'RS401', creditHours: 2),
        ],
      ),
      Semester(
        name: 'الفصل الدراسي الثاني',
        subjects: [
          Subject(name: 'مشروع التخرج', code: 'PR402', creditHours: 4),
          Subject(
            name: 'القانون الإداري المتقدم',
            code: 'LW404',
            creditHours: 3,
          ),
          Subject(name: 'فقه البيئة والطبيعة', code: 'FQ402', creditHours: 3),
          Subject(
            name: 'حقوق الإنسان في الإسلام',
            code: 'HR401',
            creditHours: 3,
          ),
          Subject(name: 'القانون المقارن', code: 'LW405', creditHours: 3),
          Subject(name: 'التدريب العملي', code: 'TR401', creditHours: 2),
        ],
      ),
    ],
  ),
];

// Home Screen
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: SafeArea(
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(),
          slivers: [
            // Modern Hero Header
            SliverToBoxAdapter(
              child: Container(
                height: 280,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      const Color(0xFF667EEA),
                      const Color(0xFF764BA2),
                      const Color(0xFF6366F1),
                    ],
                  ),
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(32),
                    bottomRight: Radius.circular(32),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF667EEA).withValues(alpha: 0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Stack(
                  children: [
                    // Header Content
                    Positioned(
                      top: 20,
                      left: 20,
                      right: 20,
                      child: Container(
                        padding: const EdgeInsets.all(24),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.15),
                          borderRadius: BorderRadius.circular(24),
                          border: Border.all(
                            color: Colors.white.withValues(alpha: 0.2),
                            width: 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 20,
                              offset: const Offset(0, 8),
                            ),
                          ],
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'أهلاً وسهلاً! 🌟',
                                    style: GoogleFonts.cairo(
                                      fontSize: 28,
                                      fontWeight: FontWeight.w800,
                                      color: Colors.white,
                                      height: 1.2,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'مرحباً بك في تطبيق كلية الشريعة والقانون',
                                    style: GoogleFonts.cairo(
                                      fontSize: 14,
                                      color: Colors.white.withValues(
                                        alpha: 0.9,
                                      ),
                                      height: 1.4,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // Notification Button
                            Container(
                              width: 50,
                              height: 50,
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: Colors.white.withValues(alpha: 0.3),
                                  width: 1,
                                ),
                              ),
                              child: IconButton(
                                onPressed: () => _showNotifications(),
                                icon: Stack(
                                  children: [
                                    const Icon(
                                      Icons.notifications_rounded,
                                      color: Colors.white,
                                      size: 24,
                                    ),
                                    Positioned(
                                      right: 2,
                                      top: 2,
                                      child: Container(
                                        width: 10,
                                        height: 10,
                                        decoration: BoxDecoration(
                                          color: const Color(0xFFFF4757),
                                          borderRadius: BorderRadius.circular(
                                            5,
                                          ),
                                          border: Border.all(
                                            color: Colors.white,
                                            width: 1,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Stats Cards
                    Positioned(
                      bottom: -30,
                      left: 20,
                      right: 20,
                      child: Row(
                        children: [
                          Expanded(
                            child: _buildSimpleStatCard(
                              icon: Icons.school_rounded,
                              title: '4',
                              subtitle: 'فرق دراسية',
                              color: const Color(0xFF6366F1),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: _buildSimpleStatCard(
                              icon: Icons.auto_stories_rounded,
                              title: '50+',
                              subtitle: 'مادة دراسية',
                              color: const Color(0xFFEC4899),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: _buildSimpleStatCard(
                              icon: Icons.picture_as_pdf_rounded,
                              title: '200+',
                              subtitle: 'ملف PDF',
                              color: const Color(0xFF10B981),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Spacing
            const SliverToBoxAdapter(child: SizedBox(height: 50)),

            // Section Title
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الفرق الدراسية',
                      style: GoogleFonts.cairo(
                        fontSize: 24,
                        fontWeight: FontWeight.w700,
                        color: const Color(0xFF1F2937),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'اختر الفرقة الدراسية للوصول إلى المواد والمحاضرات',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        color: const Color(0xFF6B7280),
                      ),
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),

            // Academic Years List
            SliverPadding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              sliver: SliverList(
                delegate: SliverChildBuilderDelegate((context, index) {
                  final year = academicYears[index];
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: _buildSimpleYearCard(year, index),
                  );
                }, childCount: academicYears.length),
              ),
            ),

            // Bottom spacing
            const SliverToBoxAdapter(child: SizedBox(height: 100)),
          ],
        ),
      ),
    );
  }

  Widget _buildSimpleStatCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
        border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
      ),
      child: Column(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 20,
              fontWeight: FontWeight.w800,
              color: const Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: const Color(0xFF6B7280),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSimpleYearCard(AcademicYear year, int index) {
    return GestureDetector(
      onTap: () => _navigateToYear(year),
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [year.color, year.color.withValues(alpha: 0.8)],
          ),
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: year.color.withValues(alpha: 0.3),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    year.title,
                    style: GoogleFonts.cairo(
                      fontSize: 22,
                      fontWeight: FontWeight.w800,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    year.description,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    '${year.semesters.length} فصول دراسية',
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.white.withValues(alpha: 0.8),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.arrow_forward_rounded,
                color: Colors.white,
                size: 28,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showNotifications() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(Icons.notifications, color: Color(0xFF6366F1)),
                SizedBox(width: 8),
                Text('الإشعارات'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [Text('لا توجد إشعارات جديدة')],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  void _navigateToYear(AcademicYear year) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => YearDetailsScreen(year: year)),
    );
  }
}

// Community Screen
class CommunityScreen extends StatelessWidget {
  const CommunityScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: SafeArea(
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [const Color(0xFF667EEA), const Color(0xFF764BA2)],
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      'مجتمع الطلاب',
                      style: GoogleFonts.cairo(
                        fontSize: 24,
                        fontWeight: FontWeight.w700,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  Icon(Icons.people, color: Colors.white, size: 28),
                ],
              ),
            ),
            // Content
            Expanded(
              child: Center(
                child: Text(
                  'قريباً...',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    color: const Color(0xFF6B7280),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Profile Screen
class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  bool _isDarkMode = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              // Profile Header
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [const Color(0xFF667EEA), const Color(0xFF764BA2)],
                  ),
                ),
                child: Column(
                  children: [
                    CircleAvatar(
                      radius: 50,
                      backgroundColor: Colors.white.withValues(alpha: 0.2),
                      child: Icon(Icons.person, size: 50, color: Colors.white),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'اسم الطالب',
                      style: GoogleFonts.cairo(
                        fontSize: 24,
                        fontWeight: FontWeight.w700,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      'طالب في كلية الشريعة والقانون',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              // Settings
              Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    _buildSettingItem(
                      icon: Icons.edit,
                      title: 'تعديل الملف الشخصي',
                      onTap: () {},
                    ),
                    _buildSettingItem(
                      icon: Icons.download,
                      title: 'التحميلات',
                      onTap: () {},
                    ),
                    _buildSettingItem(
                      icon: _isDarkMode ? Icons.dark_mode : Icons.light_mode,
                      title: 'الوضع المظلم',
                      trailing: Switch(
                        value: _isDarkMode,
                        onChanged: (value) {
                          setState(() {
                            _isDarkMode = value;
                          });
                        },
                      ),
                      onTap: () {},
                    ),
                    _buildSettingItem(
                      icon: Icons.logout,
                      title: 'تسجيل الخروج',
                      onTap: () {},
                      isDestructive: true,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Widget? trailing,
    bool isDestructive = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: isDestructive ? Colors.red : const Color(0xFF6366F1),
        ),
        title: Text(
          title,
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.w600,
            color: isDestructive ? Colors.red : const Color(0xFF1F2937),
          ),
        ),
        trailing: trailing ?? Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }
}

// Year Details Screen
class YearDetailsScreen extends StatelessWidget {
  final AcademicYear year;

  const YearDetailsScreen({super.key, required this.year});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: AppBar(
        title: Text(
          year.title,
          style: GoogleFonts.cairo(fontWeight: FontWeight.w700),
        ),
        backgroundColor: year.color,
        foregroundColor: Colors.white,
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(20),
        itemCount: year.semesters.length,
        itemBuilder: (context, index) {
          final semester = year.semesters[index];
          return Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: ExpansionTile(
              title: Text(
                semester.name,
                style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
              ),
              children:
                  semester.subjects.map((subject) {
                    return ListTile(
                      title: Text(subject.name, style: GoogleFonts.cairo()),
                      subtitle: Text(
                        '${subject.code} - ${subject.creditHours} ساعات',
                      ),
                      trailing: Icon(Icons.book),
                    );
                  }).toList(),
            ),
          );
        },
      ),
    );
  }
}
