import '../models/subject.dart';

class AcademicData {
  static List<AcademicYear> getAcademicYears() {
    return [
      // الفرقة الأولى
      AcademicYear(
        id: 'year1',
        name: 'First Year',
        arabicName: 'الفرقة الأولى',
        color: '#6366F1',
        gradientStart: '#6366F1',
        gradientEnd: '#8B5CF6',
        semesters: [
          Semester(
            id: 'year1_sem1',
            name: 'First Semester',
            arabicName: 'الترم الأول',
            subjects: [
              Subject(
                id: 'fiqh_issues',
                name: 'Contemporary Jurisprudential Issues',
                arabicName: 'قضايا فقهية معاصرة',
                credits: 2,
                pdfFiles: ['fiqh_issues.pdf'],
                description: 'دراسة القضايا الفقهية المعاصرة والمستجدات',
              ),
              Subject(
                id: 'legislation_history',
                name: 'History of Legislation',
                arabicName: 'تاريخ التشريع',
                credits: 2,
                pdfFiles: ['legislation_history.pdf'],
                description: 'تاريخ التشريع الإسلامي والقانوني',
              ),
              Subject(
                id: 'hadith_terminology',
                name: 'Hadith Terminology and Men',
                arabicName: 'مصطلح الحديث ورجاله',
                credits: 3,
                pdfFiles: ['hadith_terminology.pdf'],
                description: 'علم مصطلح الحديث ودراسة الرجال',
              ),
              Subject(
                id: 'criminology',
                name: 'Criminology and Punishment',
                arabicName: 'علم الإجرام والعقاب',
                credits: 2,
                pdfFiles: ['criminology.pdf'],
                description: 'دراسة علم الإجرام ونظريات العقاب',
              ),
              Subject(
                id: 'legal_history',
                name: 'History of Law',
                arabicName: 'تاريخ القانون',
                credits: 3,
                pdfFiles: ['legal_history.pdf'],
                description: 'تاريخ القانون وتطوره عبر العصور',
              ),
            ],
          ),
          Semester(
            id: 'year1_sem2',
            name: 'Second Semester',
            arabicName: 'الترم الثاني',
            subjects: [
              Subject(
                id: 'quran',
                name: 'Holy Quran',
                arabicName: 'القرآن الكريم',
                credits: 2,
                pdfFiles: ['quran.pdf'],
                description: 'دراسة القرآن الكريم وعلومه',
              ),
              Subject(
                id: 'international_law',
                name: 'Public International Law',
                arabicName: 'القانون الدولي العام',
                credits: 3,
                pdfFiles: ['international_law.pdf'],
                description: 'مبادئ القانون الدولي العام',
              ),
              Subject(
                id: 'usul_fiqh',
                name: 'Principles of Jurisprudence',
                arabicName: 'أصول الفقه (حنفي / غير حنفي)',
                credits: 4,
                pdfFiles: ['usul_fiqh.pdf'],
                description: 'أصول الفقه الإسلامي',
              ),
              Subject(
                id: 'foreign_language',
                name: 'Foreign Language',
                arabicName: 'اللغة الأجنبية',
                credits: 2,
                pdfFiles: ['foreign_language.pdf'],
                description: 'اللغة الإنجليزية أو الفرنسية',
              ),
              Subject(
                id: 'political_systems',
                name: 'Political and Constitutional Systems',
                arabicName: 'النظم السياسية والدستورية',
                credits: 3,
                pdfFiles: ['political_systems.pdf'],
                description: 'دراسة النظم السياسية والدستورية',
              ),
            ],
          ),
        ],
      ),

      // الفرقة الثانية
      AcademicYear(
        id: 'year2',
        name: 'Second Year',
        arabicName: 'الفرقة الثانية',
        color: '#EC4899',
        gradientStart: '#EC4899',
        gradientEnd: '#F97316',
        semesters: [
          Semester(
            id: 'year2_sem1',
            name: 'First Semester',
            arabicName: 'الترم الأول',
            subjects: [
              Subject(
                id: 'international_organizations',
                name: 'International Organizations',
                arabicName: 'المنظمات الدولية',
                credits: 2,
                pdfFiles: ['international_organizations.pdf'],
                description: 'دراسة المنظمات الدولية ودورها',
              ),
              Subject(
                id: 'comparative_fiqh',
                name: 'Comparative Jurisprudence',
                arabicName: 'فقه مقارن',
                credits: 3,
                pdfFiles: ['comparative_fiqh.pdf'],
                description: 'المقارنة بين المذاهب الفقهية',
              ),
              Subject(
                id: 'economics',
                name: 'Economics',
                arabicName: 'الاقتصاد',
                credits: 2,
                pdfFiles: ['economics.pdf'],
                description: 'مبادئ علم الاقتصاد',
              ),
              Subject(
                id: 'contemporary_fiqh_issues',
                name: 'Contemporary Jurisprudential Issues',
                arabicName: 'قضايا فقهية معاصرة',
                credits: 2,
                pdfFiles: ['contemporary_fiqh_issues.pdf'],
                description: 'القضايا الفقهية المعاصرة والمستجدات',
              ),
            ],
          ),
          Semester(
            id: 'year2_sem2',
            name: 'Second Semester',
            arabicName: 'الترم الثاني',
            subjects: [
              Subject(
                id: 'quran_2',
                name: 'Holy Quran',
                arabicName: 'القرآن الكريم',
                credits: 2,
                pdfFiles: ['quran_2.pdf'],
                description: 'دراسة القرآن الكريم وعلومه',
              ),
              Subject(
                id: 'civil_law',
                name: 'Civil Law',
                arabicName: 'القانون المدني',
                credits: 4,
                pdfFiles: ['civil_law.pdf'],
                description: 'أحكام القانون المدني',
              ),
              Subject(
                id: 'administrative_law',
                name: 'Administrative Law',
                arabicName: 'القانون الإداري',
                credits: 3,
                pdfFiles: ['administrative_law.pdf'],
                description: 'مبادئ القانون الإداري',
              ),
              Subject(
                id: 'criminal_law',
                name: 'Criminal Law',
                arabicName: 'القانون الجنائي',
                credits: 4,
                pdfFiles: ['criminal_law.pdf'],
                description: 'أحكام القانون الجنائي',
              ),
            ],
          ),
        ],
      ),

      // الفرقة الثالثة
      AcademicYear(
        id: 'year3',
        name: 'Third Year',
        arabicName: 'الفرقة الثالثة',
        color: '#10B981',
        gradientStart: '#10B981',
        gradientEnd: '#06B6D4',
        semesters: [
          Semester(
            id: 'year3_sem1',
            name: 'First Semester',
            arabicName: 'الترم الأول',
            subjects: [
              Subject(
                id: 'civil_contracts',
                name: 'Civil Law (Contracts)',
                arabicName: 'القانون المدني (عقود)',
                credits: 4,
                pdfFiles: ['civil_contracts.pdf'],
                description: 'دراسة العقود في القانون المدني',
              ),
              Subject(
                id: 'money_banking',
                name: 'Money and Banking',
                arabicName: 'النقود والبنوك',
                credits: 2,
                pdfFiles: ['money_banking.pdf'],
                description: 'دراسة النقود والنظام المصرفي',
              ),
              Subject(
                id: 'legal_terminology',
                name: 'Legal Terminology',
                arabicName: 'المصطلحات القانونية',
                credits: 2,
                pdfFiles: ['legal_terminology.pdf'],
                description: 'المصطلحات القانونية باللغات المختلفة',
              ),
            ],
          ),
          Semester(
            id: 'year3_sem2',
            name: 'Second Semester',
            arabicName: 'الترم الثاني',
            subjects: [
              Subject(
                id: 'commercial_law',
                name: 'Commercial Law',
                arabicName: 'قانون تجاري',
                credits: 3,
                pdfFiles: ['commercial_law.pdf'],
                description: 'أحكام القانون التجاري',
              ),
              Subject(
                id: 'civil_procedures',
                name: 'Civil Procedures',
                arabicName: 'قانون المرافعات',
                credits: 3,
                pdfFiles: ['civil_procedures.pdf'],
                description: 'إجراءات التقاضي المدني',
              ),
              Subject(
                id: 'personal_status_muslims',
                name: 'Personal Status for Muslims',
                arabicName: 'أحوال شخصية للمسلمين',
                credits: 3,
                pdfFiles: ['personal_status_muslims.pdf'],
                description: 'أحكام الأحوال الشخصية للمسلمين',
              ),
            ],
          ),
        ],
      ),

      // الفرقة الرابعة
      AcademicYear(
        id: 'year4',
        name: 'Fourth Year',
        arabicName: 'الفرقة الرابعة',
        color: '#F59E0B',
        gradientStart: '#F59E0B',
        gradientEnd: '#EF4444',
        semesters: [
          Semester(
            id: 'year4_sem1',
            name: 'First Semester',
            arabicName: 'الترم الأول',
            subjects: [
              Subject(
                id: 'forced_execution',
                name: 'Forced Execution',
                arabicName: 'التنفيذ الجبري',
                credits: 3,
                pdfFiles: ['forced_execution.pdf'],
                description: 'إجراءات التنفيذ الجبري',
              ),
              Subject(
                id: 'commercial_law_advanced',
                name: 'Commercial Law (Advanced)',
                arabicName: 'القانون التجاري',
                credits: 3,
                pdfFiles: ['commercial_law_advanced.pdf'],
                description: 'القانون التجاري المتقدم',
              ),
              Subject(
                id: 'administrative_contracts',
                name: 'Administrative Contracts',
                arabicName: 'العقود الإدارية',
                credits: 3,
                pdfFiles: ['administrative_contracts.pdf'],
                description: 'دراسة العقود الإدارية',
              ),
              Subject(
                id: 'inheritance',
                name: 'Inheritance',
                arabicName: 'الميراث',
                credits: 3,
                pdfFiles: ['inheritance.pdf'],
                description: 'أحكام الميراث في الشريعة الإسلامية',
              ),
            ],
          ),
          Semester(
            id: 'year4_sem2',
            name: 'Second Semester',
            arabicName: 'الترم الثاني',
            subjects: [
              Subject(
                id: 'property_insurance',
                name: 'Property and Insurance',
                arabicName: 'الملكية والتأمينات',
                credits: 3,
                pdfFiles: ['property_insurance.pdf'],
                description: 'قانون الملكية والتأمينات',
              ),
              Subject(
                id: 'criminal_procedures',
                name: 'Criminal Procedures',
                arabicName: 'الإجراءات الجنائية',
                credits: 4,
                pdfFiles: ['criminal_procedures.pdf'],
                description: 'إجراءات التقاضي الجنائي',
              ),
              Subject(
                id: 'labor_law',
                name: 'Labor Law',
                arabicName: 'قانون العمل',
                credits: 3,
                pdfFiles: ['labor_law.pdf'],
                description: 'أحكام قانون العمل',
              ),
              Subject(
                id: 'public_finance',
                name: 'Public Finance and Tax Legislation',
                arabicName: 'المالية العامة والتشريع الضريبي',
                credits: 3,
                pdfFiles: ['public_finance.pdf'],
                description: 'المالية العامة والضرائب',
              ),
            ],
          ),
        ],
      ),
    ];
  }
}
